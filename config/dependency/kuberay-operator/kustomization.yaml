kind: Kustomization

# Ku<PERSON>Ray helm package doesn't allow namespace setting at this moment, we can not export resources with namespace specified
# The workaround is to use kustomize namespace override to create under aibrix-system namespace.

resources:
- templates/deployment.yaml
- templates/leader_election_role.yaml
- templates/leader_election_role_binding.yaml
- templates/ray_rayjob_editor_role.yaml
- templates/ray_rayjob_viewer_role.yaml
- templates/ray_rayservice_editor_role.yaml
- templates/ray_rayservice_viewer_role.yaml
- templates/role.yaml
- templates/rolebinding.yaml
- templates/service.yaml
- templates/serviceaccount.yaml

images:
  - name: quay.io/kuberay/operator
    newName: quay.io/kuberay/operator
    newTag: v1.2.1