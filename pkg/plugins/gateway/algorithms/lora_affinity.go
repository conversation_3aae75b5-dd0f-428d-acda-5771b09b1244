/*
Copyright 2024 The Aibrix Team.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package routingalgorithms

import (
	"fmt"
	"math/rand"

	"github.com/vllm-project/aibrix/pkg/cache"
	"github.com/vllm-project/aibrix/pkg/metrics"
	"github.com/vllm-project/aibrix/pkg/types"
	v1 "k8s.io/api/core/v1"
	klog "k8s.io/klog/v2"
)

var (
	LoraAffinity types.RoutingAlgorithm = "lora-affinity"
)

func init() {
	Register(LoraAffinity, NewLeastRequestRouter)
}

type loraAffinityRouter struct {
	cache cache.Cache
}

func NewLoraAffinityRouter() (types.Router, error) {
	c, err := cache.Get()
	if err != nil {
		return nil, err
	}

	return loraAffinityRouter{
		cache: c,
	}, nil
}

// Route request based of least active request among input ready pods
func (r loraAffinityRouter) Route(ctx *types.RoutingContext, readyPodList types.PodList) (string, error) {
	var targetPod *v1.Pod

	affinity_pods := make([]*v1.Pod, 0)
	available_pods := make([]*v1.Pod, 0)

	for _, pod := range readyPodList.All() {
		runningLoraAdapters, err := r.cache.GetMetricValueByPodModel(pod.Name, pod.Namespace, ctx.Model, metrics.RunningLoraAdapters)
		if err == nil {
			affinity_pods = append(affinity_pods, pod)
			continue
		}
		waitingLoraAdapters, err := r.cache.GetMetricValueByPodModel(pod.Name, pod.Namespace, ctx.Model, metrics.WaitingLoraAdapters)
		if err == nil {
			affinity_pods = append(affinity_pods, pod)
			continue
		}
		available_pods = append(available_pods, pod)
		klog.V(4).Infof("pod: %v, podIP: %v, runningLoraAdapters: %v, waitingLoraAdapters: %v",
			pod.Name, pod.Status.PodIP, runningLoraAdapters.GetLabelValue(), waitingLoraAdapters.GetLabelValue())
	}

	// Use fallback if no valid metrics
	if targetPod == nil {
		var err error
		targetPod, err = SelectRandomPodAsFallback(ctx, readyPodList.All(), rand.Intn)
		if targetPod == nil {
			return "", fmt.Errorf("no pods to forward request")
		}
		if err != nil {
			return "", err
		}
	}

	klog.V(4).Infof("targetPod: %s(%s)", targetPod.Name, targetPod.Status.PodIP)
	ctx.SetTargetPod(targetPod)
	return ctx.TargetAddress(), nil
}
