# (mandatory) 
# Path to coverage profile file (output of `go test -coverprofile` command).
#
# For cases where there are many coverage profiles, such as when running 
# unit tests and integration tests separately, you can combine all those
# profiles into one. In this case, the profile should have a comma-separated list 
# of profile files, e.g., 'cover_unit.out,cover_integration.out'.
profile: cover.out

# Holds coverage thresholds percentages, values should be in range [0-100].
threshold:
  # (optional; default 0) 
  # Minimum coverage percentage required for individual files.
  file: 90

  # (optional; default 0) 
  # Minimum coverage percentage required for each package.
  package: 90

  # (optional; default 0) 
  # Minimum overall project coverage percentage required.
  total: 90

# # Holds regexp rules which will override thresholds for matched files or packages 
# # using their paths.
# #
# # First rule from this list that matches file or package is going to apply 
# # new threshold to it. If project has multiple rules that match same path, 
# # override rules should be listed in order from specific to more general rules.
# override:
#   # Increase coverage threshold to 100% for `foo` package 
#   # (default is 80, as configured above in this example).
#   - path: ^pkg/lib/foo$
#     threshold: 100

# Holds regexp rules which will exclude matched files or packages 
# from coverage statistics.
exclude:
  # Exclude files or packages matching their paths
  paths:
    - \.pb\.go$    # excludes all protobuf generated files
    - ^pkg/client     # exclude package `pkg/client`
    - ^pkg/webhook     # exclude package `pkg/webhook`

# File name of go-test-coverage breakdown file, which can be used to 
# analyze coverage difference.
breakdown-file-name: ''

diff:
  # File name of go-test-coverage breakdown file which will be used to 
  # report coverage difference.
  base-breakdown-file-name: ''